api: propbolt
languages:
  java:
    sdk_name: propboltjava
    group_id: com.yourorg
  python:
    sdk_name: propbolt_py
  typescript:
    sdk_name: propbolt_ts
  rust:
    sdk_name: propbolt_rs
    cli: false
  go:
    sdk_name: propbolt_go
  csharp:
    sdk_name: PropboltCSharp
servers:
- url: https://api.realestateapi.com
  name: environment
  default: true
auths:
- id: ApiKeyAuth
  param: api_key
default_module_structure: path
modules:
- path: v1.property_parcel
  operations:
  - id: property-parcel-api
    function_name: create
- path: v2.address_verification
  operations:
  - id: address-verification-api
    function_name: create
- path: v2.auto_complete
  operations:
  - id: autocomplete-api
    function_name: create
- path: v2.csv_builder
  operations:
  - id: csv-generator-api
    function_name: create
- path: v2.prop_gpt
  operations:
  - id: propgpt-api
    function_name: create
- path: v2.property_avm
  operations:
  - id: lender-grade-avm-api
    function_name: create
- path: v2.property_comps
  operations:
  - id: property-comps-api
    function_name: create
- path: v2.property_detail
  operations:
  - id: property-detail-api
    function_name: create
- path: v2.property_detail_bulk
  operations:
  - id: property-detail-bulk-api
    function_name: create
- path: v2.property_mapping
  operations:
  - id: beta-mapping-pins-api
    function_name: create
- path: v2.property_search
  operations:
  - id: property-search-api
    function_name: create
- path: v2.reports.property_liens
  operations:
  - id: involuntary-lien-api
    function_name: create
- path: v3.property_comps
  operations:
  - id: v3-property-comps-api
    function_name: create
