#!/usr/bin/env python3
"""
PropBolt API Server
Production API server for real estate data endpoints.
"""

import json
import os
from typing import Optional, List, Dict, Any
import datetime
from fastapi import FastAPI, Header, HTTPException, Request
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

# Initialize FastAPI app
app = FastAPI(
    title="PropBolt API",
    description="Real Estate Data API Service",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["https://data.propbolt.com", "https://propbolt.com", "http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Sample property data
SAMPLE_PROPERTIES = {
    "123 Main St, Arlington, VA 22205": {
        "id": "prop_123456789",
        "address": {
            "full": "123 Main St, Arlington, VA 22205",
            "street": "123 Main St",
            "city": "Arlington",
            "state": "VA",
            "zip": "22205",
            "county": "Arlington County"
        },
        "property_details": {
            "bedrooms": 3,
            "bathrooms": 2.5,
            "square_feet": 1850,
            "lot_size": 0.25,
            "year_built": 1995,
            "property_type": "Single Family Residential",
            "estimated_value": 675000,
            "last_sale_date": "2021-03-15",
            "last_sale_price": 625000
        },
        "tax_info": {
            "assessed_value": 650000,
            "annual_tax": 8500,
            "tax_year": 2023
        },
        "mortgage_info": {
            "has_mortgage": True,
            "estimated_balance": 450000,
            "monthly_payment": 2800
        }
    },
    "456 Oak Ave, Herndon, VA 20170": {
        "id": "prop_987654321",
        "address": {
            "full": "456 Oak Ave, Herndon, VA 20170",
            "street": "456 Oak Ave",
            "city": "Herndon",
            "state": "VA",
            "zip": "20170",
            "county": "Fairfax County"
        },
        "property_details": {
            "bedrooms": 4,
            "bathrooms": 3,
            "square_feet": 2200,
            "lot_size": 0.35,
            "year_built": 2005,
            "property_type": "Single Family Residential",
            "estimated_value": 825000,
            "last_sale_date": "2020-08-22",
            "last_sale_price": 750000
        },
        "tax_info": {
            "assessed_value": 800000,
            "annual_tax": 10200,
            "tax_year": 2023
        },
        "mortgage_info": {
            "has_mortgage": True,
            "estimated_balance": 580000,
            "monthly_payment": 3400
        }
    }
}

# Request models
class PropertyDetailRequest(BaseModel):
    address: Optional[str] = None
    apn: Optional[str] = None
    city: Optional[str] = None
    comps: Optional[bool] = False
    county: Optional[str] = None
    exact_match: Optional[bool] = True
    fips: Optional[str] = None
    house: Optional[str] = None
    id: Optional[str] = None
    state: Optional[str] = None
    street: Optional[str] = None
    unit: Optional[str] = None
    zip: Optional[str] = None

class CSVBuilderRequest(BaseModel):
    file_name: str
    map: List[str]

# Authentication middleware
def verify_api_key(x_api_key: Optional[str] = None):
    if not x_api_key:
        raise HTTPException(status_code=401, detail="API key required")
    # In production, you would validate against a database or service
    # For now, accept the configured API key
    expected_key = os.getenv("API_KEY", "AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914")
    if x_api_key != expected_key:
        raise HTTPException(status_code=401, detail="Invalid API key")
    return x_api_key

@app.get("/")
async def root():
    return {
        "message": "Local Real Estate API Test Server",
        "version": "1.0.0",
        "endpoints": [
            "POST /v2/PropertyDetail - Get property details",
            "POST /v2/CSVBuilder - Generate CSV export",
            "GET /health - Health check"
        ]
    }

@app.get("/health")
async def health_check():
    return {"status": "healthy", "timestamp": datetime.datetime.now().isoformat()}

@app.post("/v2/PropertyDetail")
async def get_property_details(
    request: PropertyDetailRequest,
    x_api_key: str = Header(..., alias="x-api-key")
):
    """Property Detail API endpoint"""
    
    # Verify API key
    verify_api_key(x_api_key)
    
    # Find property by address
    property_data = None
    search_address = request.address
    
    if search_address:
        # Try exact match first
        if search_address in SAMPLE_PROPERTIES:
            property_data = SAMPLE_PROPERTIES[search_address]
        else:
            # Try partial match
            for addr, data in SAMPLE_PROPERTIES.items():
                if search_address.lower() in addr.lower():
                    property_data = data
                    break
    
    if not property_data:
        # Return default property if no match found
        property_data = list(SAMPLE_PROPERTIES.values())[0]
    
    # Add comparables if requested
    if request.comps:
        property_data["comparables"] = [
            {
                "address": "789 Pine St, Arlington, VA 22205",
                "sale_date": "2023-01-15",
                "sale_price": 680000,
                "bedrooms": 3,
                "bathrooms": 2,
                "square_feet": 1800,
                "distance_miles": 0.3
            },
            {
                "address": "321 Elm Dr, Arlington, VA 22205", 
                "sale_date": "2022-11-08",
                "sale_price": 695000,
                "bedrooms": 4,
                "bathrooms": 3,
                "square_feet": 1950,
                "distance_miles": 0.5
            }
        ]
    
    return {
        "success": True,
        "data": property_data,
        "request_id": f"req_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}",
        "timestamp": datetime.datetime.now().isoformat()
    }

@app.post("/v2/CSVBuilder")
async def create_csv_export(
    request: CSVBuilderRequest,
    x_api_key: str = Header(..., alias="x-api-key")
):
    """CSV Builder API endpoint"""
    
    # Verify API key
    verify_api_key(x_api_key)
    
    return {
        "success": True,
        "data": {
            "file_name": request.file_name,
            "download_url": f"https://api.example.com/downloads/{request.file_name}.csv",
            "columns": request.map,
            "estimated_rows": 1500,
            "status": "processing",
            "created_at": datetime.datetime.now().isoformat()
        },
        "request_id": f"csv_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}",
        "message": "CSV export job created successfully"
    }

@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal Server Error",
            "message": str(exc),
            "timestamp": datetime.datetime.now().isoformat(),
            "path": str(request.url)
        }
    )

if __name__ == "__main__":
    print("🚀 Starting PropBolt API Server...")
    print("📍 Server will be available at: http://localhost:8000")
    print("📖 API Documentation: http://localhost:8000/docs")
    print("🔍 Health Check: http://localhost:8000/health")
    print("\n🏠 Sample addresses you can test:")
    for addr in SAMPLE_PROPERTIES.keys():
        print(f"   - {addr}")
    print(f"\n💡 API Key: {os.getenv('API_KEY', 'AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914')}")
    print("⏹️  Press Ctrl+C to stop the server\n")

    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)
